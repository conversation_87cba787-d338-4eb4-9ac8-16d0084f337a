import { Router } from 'express'
import { z } from 'zod'
import { prisma } from '@/config/database'
import { AuthenticatedRequest, requirePermission } from '@/middleware/auth'
import { ToolSchemaDefinition } from '@/services/tool-executor'
import { logger } from '@/utils/logger'

const router = Router()

// Validation schemas
const CreateToolSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  type: z.enum(['INTERNAL', 'EXTERNAL', 'API', 'WEBHOOK']),
  schema: ToolSchemaDefinition,
  config: z.record(z.any())
})

const UpdateToolSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  type: z.enum(['INTERNAL', 'EXTERNAL', 'API', 'WEBHOOK']).optional(),
  schema: ToolSchemaDefinition.optional(),
  config: z.record(z.any()).optional(),
  isActive: z.boolean().optional()
})

const ExecuteToolSchema = z.object({
  input: z.record(z.any()),
  sessionId: z.string().optional()
})

// Get all tools
router.get('/', requirePermission('tools:read'), async (req, res, next) => {
  try {
    const { type, active } = req.query

    const where: any = {}
    if (type) where.type = type
    if (active !== undefined) where.isActive = active === 'true'

    const tools = await prisma.tool.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    })

    res.json({
      success: true,
      data: tools
    })
  } catch (error) {
    next(error)
  }
})

// Get tool by ID
router.get('/:id', requirePermission('tools:read'), async (req, res, next) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({ error: 'Tool ID is required' })
    }

    const tool = await prisma.tool.findUnique({
      where: { id },
      include: {
        agents: {
          include: {
            agent: {
              select: {
                id: true,
                name: true,
                status: true
              }
            }
          }
        }
      }
    })

    if (!tool) {
      return res.status(404).json({ error: 'Tool not found' })
    }

    res.json({
      success: true,
      data: tool
    })
  } catch (error) {
    next(error)
  }
})

// Create tool
router.post('/', requirePermission('tools:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { name, description, type, schema, config } = CreateToolSchema.parse(req.body)

    const tool = await prisma.tool.create({
      data: {
        name,
        description: description ?? null,
        type,
        schema,
        config
      }
    })

    logger.info(`Tool created: ${tool.name} by user ${user.userId}`)

    res.status(201).json({
      success: true,
      data: tool
    })
  } catch (error) {
    next(error)
  }
})

// Update tool
router.put('/:id', requirePermission('tools:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params
    const updates = UpdateToolSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({ error: 'Tool ID is required' })
    }

    const existingTool = await prisma.tool.findUnique({
      where: { id }
    })

    if (!existingTool) {
      return res.status(404).json({ error: 'Tool not found' })
    }

    // Clean up undefined values for Prisma
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    )

    const tool = await prisma.tool.update({
      where: { id },
      data: {
        ...cleanUpdates,
        updatedAt: new Date()
      }
    })

    logger.info(`Tool updated: ${tool.name} by user ${user.userId}`)

    res.json({
      success: true,
      data: tool
    })
  } catch (error) {
    next(error)
  }
})

// Delete tool
router.delete('/:id', requirePermission('tools:delete'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params

    if (!id) {
      return res.status(400).json({ error: 'Tool ID is required' })
    }

    const existingTool = await prisma.tool.findUnique({
      where: { id }
    })

    if (!existingTool) {
      return res.status(404).json({ error: 'Tool not found' })
    }

    // Check if tool is being used by any agents
    const agentTools = await prisma.agentTool.findMany({
      where: { toolId: id }
    })

    if (agentTools.length > 0) {
      return res.status(400).json({
        error: 'Cannot delete tool that is being used by agents',
        agentCount: agentTools.length
      })
    }

    await prisma.tool.delete({
      where: { id }
    })

    logger.info(`Tool deleted: ${existingTool.name} by user ${user.userId}`)

    res.json({
      success: true,
      message: 'Tool deleted successfully'
    })
  } catch (error) {
    next(error)
  }
})

// Execute tool (for testing)
router.post('/:id/execute', requirePermission('tools:write'), async (req, res, next) => {
  try {
    const user = (req as AuthenticatedRequest).user
    const { id } = req.params
    const { input, sessionId } = ExecuteToolSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({ error: 'Tool ID is required' })
    }

    const tool = await prisma.tool.findUnique({
      where: { id }
    })

    if (!tool) {
      return res.status(404).json({ error: 'Tool not found' })
    }

    if (!tool.isActive) {
      return res.status(400).json({ error: 'Tool is not active' })
    }

    // Import ToolExecutor dynamically to avoid circular dependencies
    const { ToolExecutor } = await import('@/services/tool-executor')
    const executor = new ToolExecutor()

    // Create a mock execution context for testing
    const mockContext = {
      sessionId: sessionId || crypto.randomUUID(),
      userId: user.userId,
      agentId: 'test-agent',
      variables: {},
      memory: [],
      currentNode: 'test-node',
      executionPath: [],
      startTime: new Date(),
      iterationCount: 0
    }

    const result = await executor.execute(id, input, mockContext)

    logger.info(`Tool executed: ${tool.name} by user ${user.userId}`)

    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    next(error)
  }
})

// Get tool usage statistics
router.get('/:id/stats', requirePermission('tools:read'), async (req, res, next) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({ error: 'Tool ID is required' })
    }

    const tool = await prisma.tool.findUnique({
      where: { id }
    })

    if (!tool) {
      return res.status(404).json({ error: 'Tool not found' })
    }

    // Get usage statistics from events
    const toolEvents = await prisma.event.findMany({
      where: {
        type: 'TOOL_CALL',
        data: {
          path: ['toolId'],
          equals: id
        }
      },
      select: {
        timestamp: true,
        sessionId: true
      },
      orderBy: { timestamp: 'desc' },
      take: 1000 // Limit to recent events
    })

    const uniqueSessions = new Set(toolEvents.map(e => e.sessionId)).size
    const totalCalls = toolEvents.length

    // Calculate usage over time (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentEvents = toolEvents.filter(e => e.timestamp >= thirtyDaysAgo)
    const dailyUsage = recentEvents.reduce((acc, event) => {
      const date = event.timestamp.toISOString().split('T')[0]
      if (date) {
        acc[date] = (acc[date] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    res.json({
      success: true,
      data: {
        toolId: id,
        totalCalls,
        uniqueSessions,
        recentCalls: recentEvents.length,
        dailyUsage,
        lastUsed: toolEvents[0]?.timestamp || null
      }
    })
  } catch (error) {
    next(error)
  }
})

// Get available tool types and their schemas
router.get('/types/schemas', requirePermission('tools:read'), async (_req, res, next) => {
  try {
    const schemas = {
      INTERNAL: {
        description: 'Built-in tools that run within the SynapseAI system',
        configSchema: {
          type: 'object',
          properties: {
            timeout: { type: 'number', default: 30000 }
          }
        },
        examples: ['get_current_time', 'generate_uuid', 'calculate', 'text_transform']
      },
      EXTERNAL: {
        description: 'External HTTP endpoints that accept JSON input',
        configSchema: {
          type: 'object',
          properties: {
            url: { type: 'string', format: 'uri' },
            method: { type: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE'], default: 'POST' },
            headers: { type: 'object' },
            timeout: { type: 'number', default: 30000 }
          },
          required: ['url']
        }
      },
      API: {
        description: 'REST API endpoints with authentication',
        configSchema: {
          type: 'object',
          properties: {
            baseUrl: { type: 'string', format: 'uri' },
            endpoint: { type: 'string' },
            method: { type: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE'], default: 'GET' },
            apiKey: { type: 'string' },
            authType: { type: 'string', enum: ['bearer', 'api_key', 'basic'], default: 'bearer' },
            timeout: { type: 'number', default: 30000 }
          },
          required: ['baseUrl', 'endpoint']
        }
      },
      WEBHOOK: {
        description: 'Webhook endpoints that receive event notifications',
        configSchema: {
          type: 'object',
          properties: {
            url: { type: 'string', format: 'uri' },
            secret: { type: 'string' },
            timeout: { type: 'number', default: 30000 }
          },
          required: ['url']
        }
      }
    }

    res.json({
      success: true,
      data: schemas
    })
  } catch (error) {
    next(error)
  }
})

export default router

import  { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  PencilIcon,
  PlayIcon,
  StopIcon,
  TrashIcon,
  CpuChipIcon,
  WrenchScrewdriverIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline'
import { api } from '@/services/api'
import { websocket } from '@/services/websocket'
import { Badge } from '@/components/ui/Badge'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { formatRelativeTime, getStatusColor, formatDate } from '@/lib/utils'
import { toast } from 'react-hot-toast'

export default function AgentDetailPage() {
  const { id } = useParams<{ id: string }>()
  const queryClient = useQueryClient()
  const [isRunning, setIsRunning] = useState(false)

  const { data: agent, isLoading, error } = useQuery({
    queryKey: ['agent', id],
    queryFn: () => api.getAgent(id!),
    enabled: !!id
  })

  const { data: agentStatus } = useQuery({
    queryKey: ['agent-status', id],
    queryFn: () => api.getAgentStatus(id!),
    enabled: !!id,
    refetchInterval: 5000 // Refresh every 5 seconds
  })

  const deleteAgentMutation = useMutation({
    mutationFn: () => api.deleteAgent(id!),
    onSuccess: () => {
      toast.success('Agent deleted successfully')
      queryClient.invalidateQueries({ queryKey: ['agents'] })
      // Navigate back to agents list
      window.history.back()
    }
  })

  const handleStartAgent = () => {
    if (!agent) return

    setIsRunning(true)
    websocket.startAgent(agent.id, agent.config)
    toast.success('Agent started')
  }

  const handleStopAgent = () => {
    if (!agent) return

    setIsRunning(false)
    websocket.stopAgent(agent.id)
    toast.success('Agent stopped')
  }

  const handleDeleteAgent = () => {
    if (!agent) return

    if (window.confirm(`Are you sure you want to delete "${agent.name}"? This action cannot be undone.`)) {
      deleteAgentMutation.mutate()
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error || !agent) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load agent</div>
        <Link to="/agents" className="btn btn-primary">
          Back to Agents
        </Link>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center">
            <CpuChipIcon className="h-7 w-7 text-primary-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{agent.name}</h1>
            <div className="flex items-center space-x-3 mt-1">
              <Badge variant={getStatusColor(agent.status) as any}>
                {agent.status}
              </Badge>
              <span className="text-sm text-gray-500">
                Created {formatRelativeTime(agent.createdAt)}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {agent.status === 'ACTIVE' && (
            <>
              {isRunning || agentStatus?.activeSessions > 0 ? (
                <button
                  onClick={handleStopAgent}
                  className="btn btn-outline text-red-600 border-red-300 hover:bg-red-50"
                >
                  <StopIcon className="h-5 w-5 mr-2" />
                  Stop
                </button>
              ) : (
                <button
                  onClick={handleStartAgent}
                  className="btn btn-primary"
                >
                  <PlayIcon className="h-5 w-5 mr-2" />
                  Start
                </button>
              )}
            </>
          )}

          <Link
            to={`/agents/${agent.id}/edit`}
            className="btn btn-outline"
          >
            <PencilIcon className="h-5 w-5 mr-2" />
            Edit
          </Link>

          <button
            onClick={handleDeleteAgent}
            disabled={deleteAgentMutation.isPending}
            className="btn btn-outline text-red-600 border-red-300 hover:bg-red-50"
          >
            <TrashIcon className="h-5 w-5 mr-2" />
            Delete
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          {agent.description && (
            <div className="card">
              <h2 className="text-lg font-semibold text-gray-900 mb-3">Description</h2>
              <p className="text-gray-600">{agent.description}</p>
            </div>
          )}

          {/* Flow Configuration */}
          <div className="card">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Flow Configuration</h2>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Nodes:</span>
                  <span className="ml-2 font-medium">{agent.config.nodes?.length || 0}</span>
                </div>
                <div>
                  <span className="text-gray-500">Max Execution Time:</span>
                  <span className="ml-2 font-medium">
                    {agent.config.settings?.maxExecutionTime ?
                      `${agent.config.settings.maxExecutionTime / 1000}s` :
                      '300s'
                    }
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Max Iterations:</span>
                  <span className="ml-2 font-medium">
                    {agent.config.settings?.maxIterations || 100}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Retry Attempts:</span>
                  <span className="ml-2 font-medium">
                    {agent.config.settings?.retryAttempts || 3}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Tools */}
          <div className="card">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <WrenchScrewdriverIcon className="h-5 w-5 mr-2" />
                Tools ({agent.tools?.length || 0})
              </h2>
              <button className="btn btn-outline btn-sm">
                Manage Tools
              </button>
            </div>

            {agent.tools && agent.tools.length > 0 ? (
              <div className="space-y-3">
                {agent.tools.map((agentTool) => (
                  <div key={agentTool.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <WrenchScrewdriverIcon className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{agentTool.tool.name}</p>
                        <p className="text-sm text-gray-500">{agentTool.tool.type}</p>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      Priority: {agentTool.priority}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <WrenchScrewdriverIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No tools assigned</p>
                <button className="btn btn-primary mt-4">
                  Add Tools
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status Card */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Status</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-500">Current Status</span>
                <Badge variant={getStatusColor(agent.status) as any}>
                  {agent.status}
                </Badge>
              </div>

              {agentStatus && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-500">Active Sessions</span>
                  <span className="font-medium">{agentStatus.activeSessions}</span>
                </div>
              )}

              <div className="flex items-center justify-between">
                <span className="text-gray-500">Last Updated</span>
                <span className="font-medium">{formatRelativeTime(agent.updatedAt)}</span>
              </div>
            </div>
          </div>

          {/* Metadata */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Metadata</h3>
            <div className="space-y-3 text-sm">
              <div>
                <span className="text-gray-500">Agent ID</span>
                <p className="font-mono text-xs text-gray-900 mt-1 break-all">{agent.id}</p>
              </div>

              <div>
                <span className="text-gray-500">Created By</span>
                <p className="text-gray-900 mt-1">{agent.user?.name || agent.user?.email}</p>
              </div>

              <div>
                <span className="text-gray-500">Created At</span>
                <p className="text-gray-900 mt-1">{formatDate(agent.createdAt)}</p>
              </div>

              <div>
                <span className="text-gray-500">Last Modified</span>
                <p className="text-gray-900 mt-1">{formatDate(agent.updatedAt)}</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-2">
              <Link
                to={`/sessions?agentId=${agent.id}`}
                className="btn btn-outline w-full justify-start"
              >
                <ChartBarIcon className="h-4 w-4 mr-2" />
                View Sessions
              </Link>

              <button className="btn btn-outline w-full justify-start">
                <ClockIcon className="h-4 w-4 mr-2" />
                View Logs
              </button>

              <button className="btn btn-outline w-full justify-start">
                <ChartBarIcon className="h-4 w-4 mr-2" />
                Analytics
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

import { io, Socket } from 'socket.io-client'
import { UAUI<PERSON>vent, ConnectionStatus } from '@/types'
import { toast } from 'react-hot-toast'

export type EventHandler = (event: UAUIEvent) => void
export type ConnectionHandler = (status: ConnectionStatus) => void

class WebSocketService {
  private socket: Socket | null = null
  private eventHandlers = new Map<string, Set<EventHandler>>()
  private connectionHandlers = new Set<ConnectionHandler>()
  private connectionStatus: ConnectionStatus = {
    connected: false,
    reconnectAttempts: 0
  }
  private token: string | null = null
  private sessionId: string | null = null

  constructor() {
    this.loadToken()
  }

  private loadToken() {
    this.token = localStorage.getItem('auth_token')
  }

  connect(sessionId?: string) {
    if (this.socket?.connected) {
      return
    }

    this.sessionId = sessionId || crypto.randomUUID()
    this.loadToken()

    if (!this.token) {
      console.warn('No auth token available for WebSocket connection')
      return
    }

    this.socket = io('/', {
      auth: {
        token: this.token
      },
      query: {
        sessionId: this.sessionId
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      retries: 3
    })

    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    if (!this.socket) return

    this.socket.on('connect', () => {
      this.connectionStatus = {
        connected: true,
        sessionId: this.sessionId!,
        lastActivity: new Date().toISOString(),
        reconnectAttempts: 0
      }

      this.notifyConnectionHandlers()
      toast.success('Connected to SynapseAI')
    })

    this.socket.on('disconnect', (reason) => {
      this.connectionStatus = {
        ...this.connectionStatus,
        connected: false
      }

      this.notifyConnectionHandlers()

      if (reason === 'io server disconnect') {
        toast.error('Disconnected from server')
      }
    })

    this.socket.on('connect_error', (error) => {
      this.connectionStatus = {
        ...this.connectionStatus,
        connected: false,
        reconnectAttempts: this.connectionStatus.reconnectAttempts + 1
      }

      this.notifyConnectionHandlers()

      if (error.message.includes('Authentication failed')) {
        toast.error('Authentication failed. Please login again.')
        this.disconnect()
        window.location.href = '/login'
      } else {
        console.error('WebSocket connection error:', error)
      }
    })

    this.socket.on('connected', (data) => {
      this.connectionStatus = {
        ...this.connectionStatus,
        sessionId: data.sessionId,
        lastActivity: data.timestamp
      }

      this.notifyConnectionHandlers()
    })

    this.socket.on('event', (event: UAUIEvent) => {
      this.handleEvent(event)
    })

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error)
      toast.error(error.message || 'WebSocket error occurred')
    })

    // Heartbeat
    this.socket.on('ping', () => {
      this.socket?.emit('pong')
    })

    // Setup heartbeat interval
    setInterval(() => {
      if (this.socket?.connected) {
        this.socket.emit('heartbeat')
        this.connectionStatus.lastActivity = new Date().toISOString()
      }
    }, 30000) // Every 30 seconds
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }

    this.connectionStatus = {
      connected: false,
      reconnectAttempts: 0
    }

    this.notifyConnectionHandlers()
  }

  private handleEvent(event: UAUIEvent) {
    // Update last activity
    this.connectionStatus.lastActivity = new Date().toISOString()

    // Notify specific event handlers
    const handlers = this.eventHandlers.get(event.type)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(event)
        } catch (error) {
          console.error('Error in event handler:', error)
        }
      })
    }

    // Notify global event handlers
    const globalHandlers = this.eventHandlers.get('*')
    if (globalHandlers) {
      globalHandlers.forEach(handler => {
        try {
          handler(event)
        } catch (error) {
          console.error('Error in global event handler:', error)
        }
      })
    }

    // Handle specific event types
    this.handleSpecificEvents(event)
  }

  private handleSpecificEvents(event: UAUIEvent) {
    switch (event.type) {
      case 'system.error':
        toast.error(`System Error: ${event.data.error}`)
        break

      case 'agent.start':
        toast.success(`Agent ${event.data.agentId} started`)
        break

      case 'agent.stop':
        const reason = event.data.reason
        if (reason === 'error') {
          toast.error(`Agent ${event.data.agentId} stopped due to error`)
        } else {
          toast.success(`Agent ${event.data.agentId} stopped`)
        }
        break

      case 'tool.response':
        if (!event.data.success) {
          toast.error(`Tool ${event.data.toolName} failed: ${event.data.error}`)
        }
        break

      case 'session.end':
        toast('Session ended')
        break
    }
  }

  private notifyConnectionHandlers() {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(this.connectionStatus)
      } catch (error) {
        console.error('Error in connection handler:', error)
      }
    })
  }

  // Event subscription methods
  on(eventType: string, handler: EventHandler): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, new Set())
    }

    this.eventHandlers.get(eventType)!.add(handler)

    // Return unsubscribe function
    return () => {
      const handlers = this.eventHandlers.get(eventType)
      if (handlers) {
        handlers.delete(handler)
        if (handlers.size === 0) {
          this.eventHandlers.delete(eventType)
        }
      }
    }
  }

  off(eventType: string, handler?: EventHandler) {
    if (handler) {
      const handlers = this.eventHandlers.get(eventType)
      if (handlers) {
        handlers.delete(handler)
        if (handlers.size === 0) {
          this.eventHandlers.delete(eventType)
        }
      }
    } else {
      this.eventHandlers.delete(eventType)
    }
  }

  onConnection(handler: ConnectionHandler): () => void {
    this.connectionHandlers.add(handler)

    // Immediately call with current status
    handler(this.connectionStatus)

    // Return unsubscribe function
    return () => {
      this.connectionHandlers.delete(handler)
    }
  }

  // Emit methods
  startAgent(agentId: string, config?: any) {
    if (!this.socket?.connected) {
      toast.error('Not connected to server')
      return
    }

    this.socket.emit('agent.start', {
      agentId,
      config
    })
  }

  stopAgent(agentId: string, reason = 'user_stop') {
    if (!this.socket?.connected) {
      toast.error('Not connected to server')
      return
    }

    this.socket.emit('agent.stop', {
      agentId,
      reason
    })
  }

  sendMessage(content: string, metadata?: any) {
    if (!this.socket?.connected) {
      toast.error('Not connected to server')
      return
    }

    this.socket.emit('message.send', {
      content,
      metadata
    })
  }

  executeTool(toolId: string, toolName: string, input: any, agentId: string) {
    if (!this.socket?.connected) {
      toast.error('Not connected to server')
      return
    }

    this.socket.emit('tool.execute', {
      toolId,
      toolName,
      input,
      agentId
    })
  }

  updateSession(context: any) {
    if (!this.socket?.connected) {
      toast.error('Not connected to server')
      return
    }

    this.socket.emit('session.update', {
      context
    })
  }

  // Utility methods
  getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus }
  }

  isConnected(): boolean {
    return this.connectionStatus.connected
  }

  getSessionId(): string | null {
    return this.sessionId
  }

  // Reconnect with new token
  reconnectWithToken(token: string, sessionId?: string) {
    this.token = token
    localStorage.setItem('auth_token', token)

    this.disconnect()
    this.connect(sessionId)
  }

  // Subscribe to all events (useful for debugging)
  onAnyEvent(handler: EventHandler): () => void {
    return this.on('*', handler)
  }

  // Get event history (if needed for debugging)
  private eventHistory: UAUIEvent[] = []
  private maxHistorySize = 100

  private addToHistory(event: UAUIEvent) {
    this.eventHistory.push(event)
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift()
    }
  }

  getEventHistory(): UAUIEvent[] {
    return [...this.eventHistory]
  }

  clearEventHistory() {
    this.eventHistory = []
  }
}

// Create singleton instance
export const websocket = new WebSocketService()

// React hook for easier usage
export function useWebSocket() {
  return websocket
}

export default websocket

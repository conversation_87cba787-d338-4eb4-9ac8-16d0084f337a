import { useEffect } from 'react'
import { Routes, Route, Navigate, useLocation } from 'react-router-dom'
import { useAuthStore, useIsAuthenticated } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import { websocket } from '@/services/websocket'

// Layout Components
import AuthLayout from '@/components/layouts/AuthLayout'
import DashboardLayout from '@/components/layouts/DashboardLayout'

// Auth Pages
import LoginPage from '@/pages/auth/LoginPage'
import RegisterPage from '@/pages/auth/RegisterPage'

// Dashboard Pages
import DashboardPage from '@/pages/dashboard/DashboardPage'
import AgentsPage from '@/pages/agents/AgentsPage'
import AgentDetailPage from '@/pages/agents/AgentDetailPage'
import AgentBuilderPage from '@/pages/agents/AgentBuilderPage'
import ToolsPage from '@/pages/tools/ToolsPage'
import ToolDetailPage from '@/pages/tools/ToolDetailPage'
import ProvidersPage from '@/pages/providers/ProvidersPage'
import ProviderDetailPage from '@/pages/providers/ProviderDetailPage'
import SessionsPage from '@/pages/sessions/SessionsPage'
import SessionDetailPage from '@/pages/sessions/SessionDetailPage'
import SettingsPage from '@/pages/settings/SettingsPage'
import ComponentsTestPage from '@/pages/test/ComponentsTestPage'

// Components
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import ErrorBoundary from '@/components/ui/ErrorBoundary'

function App() {
  const location = useLocation()
  const isAuthenticated = useIsAuthenticated()
  const { refreshUser, isLoading } = useAuthStore()
  const { setCurrentPage, setConnectionStatus, addEvent, setTheme, theme } = useAppStore()

  // Initialize app
  useEffect(() => {
    // Apply initial theme
    setTheme(theme)

    // Set up WebSocket event handlers
    const unsubscribeConnection = websocket.onConnection(setConnectionStatus)
    const unsubscribeEvents = websocket.onAnyEvent(addEvent)

    // Auto-refresh user if token exists
    const token = localStorage.getItem('auth_token')
    if (token && !isAuthenticated) {
      refreshUser()
    }

    return () => {
      unsubscribeConnection()
      unsubscribeEvents()
    }
  }, [])

  // Connect WebSocket when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      websocket.connect()
    } else {
      websocket.disconnect()
    }
  }, [isAuthenticated])

  // Update current page
  useEffect(() => {
    setCurrentPage(location.pathname)
  }, [location.pathname, setCurrentPage])

  // Show loading spinner during initial auth check
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={
          isAuthenticated ? <Navigate to="/dashboard" replace /> : (
            <AuthLayout>
              <LoginPage />
            </AuthLayout>
          )
        } />

        <Route path="/register" element={
          isAuthenticated ? <Navigate to="/dashboard" replace /> : (
            <AuthLayout>
              <RegisterPage />
            </AuthLayout>
          )
        } />

        {/* Protected Routes */}
        <Route path="/dashboard" element={
          isAuthenticated ? (
            <DashboardLayout>
              <DashboardPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/agents" element={
          isAuthenticated ? (
            <DashboardLayout>
              <AgentsPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/agents/:id" element={
          isAuthenticated ? (
            <DashboardLayout>
              <AgentDetailPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/agents/new" element={
          isAuthenticated ? (
            <DashboardLayout>
              <AgentBuilderPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/agents/:id/edit" element={
          isAuthenticated ? (
            <DashboardLayout>
              <AgentBuilderPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/tools" element={
          isAuthenticated ? (
            <DashboardLayout>
              <ToolsPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/tools/:id" element={
          isAuthenticated ? (
            <DashboardLayout>
              <ToolDetailPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/providers" element={
          isAuthenticated ? (
            <DashboardLayout>
              <ProvidersPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/providers/:id" element={
          isAuthenticated ? (
            <DashboardLayout>
              <ProviderDetailPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/sessions" element={
          isAuthenticated ? (
            <DashboardLayout>
              <SessionsPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/sessions/:id" element={
          isAuthenticated ? (
            <DashboardLayout>
              <SessionDetailPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/settings" element={
          isAuthenticated ? (
            <DashboardLayout>
              <SettingsPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        <Route path="/test/components" element={
          isAuthenticated ? (
            <DashboardLayout>
              <ComponentsTestPage />
            </DashboardLayout>
          ) : <Navigate to="/login" replace />
        } />

        {/* Default redirects */}
        <Route path="/" element={
          <Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />
        } />

        {/* 404 fallback */}
        <Route path="*" element={
          <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
              <p className="text-gray-600 mb-8">Page not found</p>
              <a
                href={isAuthenticated ? "/dashboard" : "/login"}
                className="btn btn-primary"
              >
                Go Home
              </a>
            </div>
          </div>
        } />
      </Routes>
    </ErrorBoundary>
  )
}

export default App

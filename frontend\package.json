{"name": "@synapseai/frontend", "version": "1.0.0", "description": "SynapseAI Frontend - React application for AI orchestration", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.8.0", "axios": "^1.6.2", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-flow-renderer": "^10.3.17", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.20.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.0.0", "zod": "^3.22.4", "zustand": "^4.4.6"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}}
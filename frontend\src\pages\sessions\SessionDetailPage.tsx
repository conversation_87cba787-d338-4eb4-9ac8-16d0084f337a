import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { ArrowLeft, Play, Square, Trash2, Activity, MessageSquare, Settings, Clock } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { Session, Event, PaginatedResponse } from '@/types'
import { api } from '@/services/api'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/Dialog'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import JsonViewer from '@/components/ui/JsonViewer'

const SessionDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [session, setSession] = useState<Session | null>(null)
  const [events, setEvents] = useState<Event[]>([])
  const [context, setContext] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [eventsLoading, setEventsLoading] = useState(false)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [deleting, setDeleting] = useState(false)

  useEffect(() => {
    if (id) {
      loadSession()
      loadEvents()
      loadContext()
    }
  }, [id])

  const loadSession = async () => {
    try {
      setLoading(true)
      const data = await api.getSession(id!)
      setSession(data)
    } catch (error) {
      console.error('Failed to load session:', error)
      toast.error('Failed to load session')
      navigate('/sessions')
    } finally {
      setLoading(false)
    }
  }

  const loadEvents = async () => {
    try {
      setEventsLoading(true)
      const response: PaginatedResponse<Event> = await api.getSessionEvents(id!, { limit: 50 })
      setEvents(response.items ?? response.data ?? [])
    } catch (error) {
      console.error('Failed to load session events:', error)
    } finally {
      setEventsLoading(false)
    }
  }

  const loadContext = async () => {
    try {
      const data = await api.getSessionContext(id!)
      setContext(data)
    } catch (error) {
      console.error('Failed to load session context:', error)
    }
  }

  const handleDelete = async () => {
    if (!session) return

    try {
      setDeleting(true)
      await api.deleteSession(session.id)
      toast.success('Session deleted successfully')
      navigate('/sessions')
    } catch (error) {
      console.error('Failed to delete session:', error)
      toast.error('Failed to delete session')
    } finally {
      setDeleting(false)
    }
  }

  const handleStartAgent = async () => {
    if (!session?.agentId) return

    try {
      await api.startAgentInSession(session.id, session.agentId)
      toast.success('Agent started successfully')
      loadSession() // Refresh session data
    } catch (error) {
      console.error('Failed to start agent:', error)
      toast.error('Failed to start agent')
    }
  }

  const handleStopAgent = async () => {
    if (!session) return

    try {
      await api.stopAgentInSession(session.id)
      toast.success('Agent stopped successfully')
      loadSession() // Refresh session data
    } catch (error) {
      console.error('Failed to stop agent:', error)
      toast.error('Failed to stop agent')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800'
      case 'INACTIVE': return 'bg-gray-100 text-gray-800'
      case 'EXPIRED': return 'bg-yellow-100 text-yellow-800'
      case 'TERMINATED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case 'USER_MESSAGE': return 'bg-blue-100 text-blue-800'
      case 'AGENT_MESSAGE': return 'bg-green-100 text-green-800'
      case 'TOOL_CALL': return 'bg-purple-100 text-purple-800'
      case 'TOOL_RESPONSE': return 'bg-orange-100 text-orange-800'
      case 'ERROR': return 'bg-red-100 text-red-800'
      case 'SYSTEM': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDuration = (createdAt: string, expiresAt?: string | null) => {
    const start = new Date(createdAt)
    const end = expiresAt ? new Date(expiresAt) : new Date()
    const duration = end.getTime() - start.getTime()
    const hours = Math.floor(duration / (1000 * 60 * 60))
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
    return `${hours}h ${minutes}m`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!session) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 text-lg mb-2">Session not found</div>
        <Button onClick={() => navigate('/sessions')}>
          Back to Sessions
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={() => navigate('/sessions')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Session {session.id.slice(0, 8)}...
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <Badge className={getStatusColor(session.status)}>
                {session.status}
              </Badge>
              {session.agent && (
                <Badge variant="default">
                  Agent: {session.agent.name}
                </Badge>
              )}
              {session.provider && (
                <Badge variant="default">
                  Provider: {session.provider.name}
                </Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {session.status === 'ACTIVE' && session.agentId && (
            <Button variant="outline" onClick={handleStopAgent}>
              <Square className="h-4 w-4 mr-2" />
              Stop Agent
            </Button>
          )}
          {session.status === 'INACTIVE' && session.agentId && (
            <Button variant="outline" onClick={handleStartAgent}>
              <Play className="h-4 w-4 mr-2" />
              Start Agent
            </Button>
          )}
          <Button variant="destructive" onClick={() => setDeleteDialogOpen(true)}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="context">Context</TabsTrigger>
          <TabsTrigger value="metadata">Metadata</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Session Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <Badge className={getStatusColor(session.status)}>{session.status}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Duration:</span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {formatDuration(session.createdAt, session.expiresAt)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Events:</span>
                  <span>{events.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span>{new Date(session.createdAt).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Updated:</span>
                  <span>{new Date(session.updatedAt).toLocaleString()}</span>
                </div>
                {session.expiresAt && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Expires:</span>
                    <span>{new Date(session.expiresAt).toLocaleString()}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {session.agent && (
              <Card>
                <CardHeader>
                  <CardTitle>Agent Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Name:</span>
                    <span>{session.agent.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <Badge>{session.agent.status}</Badge>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => navigate(`/agents/${session.agentId}`)}
                    className="w-full"
                  >
                    View Agent Details
                  </Button>
                </CardContent>
              </Card>
            )}

            {session.provider && (
              <Card>
                <CardHeader>
                  <CardTitle>Provider Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Name:</span>
                    <span>{session.provider.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Type:</span>
                    <Badge>{session.provider.type}</Badge>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => navigate(`/providers/${session.providerId}`)}
                    className="w-full"
                  >
                    View Provider Details
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="events" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Session Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              {eventsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <LoadingSpinner size="lg" />
                </div>
              ) : events.length > 0 ? (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {events.map((event) => (
                    <div key={event.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <Badge className={getEventTypeColor(event.type)}>
                          {event.type}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {new Date(event.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <JsonViewer data={event.data} />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No events recorded for this session
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="context" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Session Context
              </CardTitle>
            </CardHeader>
            <CardContent>
              {context ? (
                <JsonViewer data={context} />
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No context data available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metadata" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Session Metadata
              </CardTitle>
            </CardHeader>
            <CardContent>
              {session.metadata ? (
                <JsonViewer data={session.metadata} />
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No metadata available
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Session</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this session? This action cannot be undone and will remove all associated events.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDelete}
              disabled={deleting}
            >
              {deleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default SessionDetailPage

import React, { useState, useEffect } from 'react'
import {  useNavigate } from 'react-router-dom'
import { Plus, Search, MoreVertical, Trash2, Edit, Eye, Activity, TestTube } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { Provider, ProviderType } from '@/types'
import { api } from '@/services/api'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Badge } from '@/components/ui/Badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/DropdownMenu'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/Dialog'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

const ProvidersPage: React.FC = () => {
  const navigate = useNavigate()
  const [providers, setProviders] = useState<Provider[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<ProviderType | 'ALL'>('ALL')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [providerToDelete, setProviderToDelete] = useState<Provider | null>(null)
  const [deleting, setDeleting] = useState(false)
  const [healthChecking, setHealthChecking] = useState<string | null>(null)

  useEffect(() => {
    loadProviders()
  }, [])

  const loadProviders = async () => {
    try {
      setLoading(true)
      const data = await api.getProviders()
      setProviders(data)
    } catch (error) {
      console.error('Failed to load providers:', error)
      toast.error('Failed to load providers')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteProvider = async () => {
    if (!providerToDelete) return

    try {
      setDeleting(true)
      await api.deleteProvider(providerToDelete.id)
      setProviders(providers.filter(provider => provider.id !== providerToDelete.id))
      toast.success('Provider deleted successfully')
      setDeleteDialogOpen(false)
      setProviderToDelete(null)
    } catch (error) {
      console.error('Failed to delete provider:', error)
      toast.error('Failed to delete provider')
    } finally {
      setDeleting(false)
    }
  }

  const handleHealthCheck = async (provider: Provider) => {
    try {
      setHealthChecking(provider.id)
      const health = await api.getProviderHealth(provider.id)
      toast.success(`Provider is ${health.status === 'healthy' ? 'healthy' : 'unhealthy'}`)
    } catch (error) {
      console.error('Failed to check provider health:', error)
      toast.error('Failed to check provider health')
    } finally {
      setHealthChecking(null)
    }
  }

  const handleTestProvider = async (provider: Provider) => {
    try {
      const testMessages = [{ role: 'user', content: 'Hello, this is a test message.' }]
      await api.testProvider(provider.id, testMessages)
      toast.success('Provider test completed successfully')
    } catch (error) {
      console.error('Failed to test provider:', error)
      toast.error('Failed to test provider')
    }
  }

  const filteredProviders = providers.filter(provider => {
    const matchesSearch = provider.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'ALL' || provider.type === filterType
    return matchesSearch && matchesType
  })

  const getTypeColor = (type: ProviderType) => {
    switch (type) {
      case 'OPENAI': return 'bg-green-100 text-green-800'
      case 'ANTHROPIC': return 'bg-orange-100 text-orange-800'
      case 'GOOGLE': return 'bg-blue-100 text-blue-800'
      case 'CUSTOM': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">AI Providers</h1>
          <p className="text-gray-600 mt-1">Manage your AI model providers and configurations</p>
        </div>
        <Button onClick={() => navigate('/providers/new')} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Provider
        </Button>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search providers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value as ProviderType | 'ALL')}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="ALL">All Types</option>
          <option value="OPENAI">OpenAI</option>
          <option value="ANTHROPIC">Anthropic</option>
          <option value="GOOGLE">Google</option>
          <option value="CUSTOM">Custom</option>
        </select>
      </div>

      {/* Providers Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredProviders.map((provider) => (
          <Card key={provider.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-2">
              <div className="flex-1">
                <CardTitle className="text-lg font-semibold">{provider.name}</CardTitle>
                <Badge className={`mt-2 ${getTypeColor(provider.type)}`}>
                  {provider.type}
                </Badge>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => navigate(`/providers/${provider.id}`)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate(`/providers/${provider.id}/edit`)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleHealthCheck(provider)}>
                    <Activity className="h-4 w-4 mr-2" />
                    Health Check
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleTestProvider(provider)}>
                    <TestTube className="h-4 w-4 mr-2" />
                    Test Provider
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => {
                      setProviderToDelete(provider)
                      setDeleteDialogOpen(true)
                    }}
                    className="text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Status:</span>
                  <span className={`px-2 py-1 rounded-full text-xs ${
                    provider.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {provider.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Tenant:</span>
                  <span className="text-gray-900">
                    {provider.tenantId ? 'Tenant Specific' : 'Global'}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Updated {new Date(provider.updatedAt).toLocaleDateString()}</span>
                  {healthChecking === provider.id && (
                    <LoadingSpinner size="sm" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredProviders.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">No providers found</div>
          <p className="text-gray-600 mb-4">
            {searchTerm || filterType !== 'ALL' 
              ? 'Try adjusting your search or filters' 
              : 'Get started by adding your first AI provider'
            }
          </p>
          {!searchTerm && filterType === 'ALL' && (
            <Button onClick={() => navigate('/providers/new')}>
              Add Your First Provider
            </Button>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Provider</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{providerToDelete?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteProvider}
              disabled={deleting}
            >
              {deleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ProvidersPage

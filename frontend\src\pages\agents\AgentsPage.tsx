import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import {
  PlusIcon,
  CpuChipIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EllipsisVerticalIcon
} from '@heroicons/react/24/outline'
import { api } from '@/services/api'
import { Badge } from '@/components/ui/Badge'
import LoadingSpinner from '@/components/ui/LoadingSpinner'
import { formatRelativeTime, getStatusColor } from '@/lib/utils'
import { Agent } from '@/types'

export default function AgentsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  const { data: agents, isLoading, error } = useQuery({
    queryKey: ['agents'],
    queryFn: api.getAgents
  })

  // Filter agents based on search and status
  const filteredAgents = agents?.filter(agent => {
    const matchesSearch = agent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      agent.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || agent.status === statusFilter

    return matchesSearch && matchesStatus
  }) || []

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Failed to load agents</div>
        <button
          onClick={() => window.location.reload()}
          className="btn btn-primary"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Agents</h1>
          <p className="text-gray-600">
            Manage your AI agents and their configurations
          </p>
        </div>
        <Link to="/agents/new" className="btn btn-primary">
          <PlusIcon className="h-5 w-5 mr-2" />
          Create Agent
        </Link>
      </div>

      {/* Filters and Search */}
      <div className="card">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search agents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input pl-10"
            />
          </div>

          {/* Status Filter */}
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="select"
            >
              <option value="all">All Status</option>
              <option value="ACTIVE">Active</option>
              <option value="INACTIVE">Inactive</option>
              <option value="TRAINING">Training</option>
              <option value="ERROR">Error</option>
            </select>
          </div>
        </div>
      </div>

      {/* Agents Grid */}
      {filteredAgents.length === 0 ? (
        <div className="text-center py-12">
          <CpuChipIcon className="h-16 w-16 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm || statusFilter !== 'all' ? 'No agents found' : 'No agents yet'}
          </h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || statusFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Create your first AI agent to get started'
            }
          </p>
          {(!searchTerm && statusFilter === 'all') && (
            <Link to="/agents/new" className="btn btn-primary">
              <PlusIcon className="h-5 w-5 mr-2" />
              Create Agent
            </Link>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAgents.map((agent) => (
            <AgentCard key={agent.id} agent={agent} />
          ))}
        </div>
      )}
    </div>
  )
}

interface AgentCardProps {
  agent: Agent
}

function AgentCard({ agent }: AgentCardProps) {
  return (
    <div className="card hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
            <CpuChipIcon className="h-6 w-6 text-primary-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">{agent.name}</h3>
            <Badge variant={getStatusColor(agent.status) as any}>
              {agent.status}
            </Badge>
          </div>
        </div>

        <button className="p-1 text-gray-400 hover:text-gray-600">
          <EllipsisVerticalIcon className="h-5 w-5" />
        </button>
      </div>

      {agent.description && (
        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
          {agent.description}
        </p>
      )}

      <div className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Sessions</span>
          <span className="font-medium">{agent._count?.sessions || 0}</span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Tools</span>
          <span className="font-medium">{agent.tools?.length || 0}</span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">Updated</span>
          <span className="font-medium">{formatRelativeTime(agent.updatedAt)}</span>
        </div>
      </div>

      <div className="mt-6 pt-4 border-t border-gray-200 flex space-x-2">
        <Link
          to={`/agents/${agent.id}`}
          className="btn btn-outline flex-1 text-sm"
        >
          View Details
        </Link>
        <Link
          to={`/agents/${agent.id}/edit`}
          className="btn btn-primary flex-1 text-sm"
        >
          Edit
        </Link>
      </div>
    </div>
  )
}

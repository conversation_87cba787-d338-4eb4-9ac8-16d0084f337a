import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Plus, Search, Filter, MoreVertical, Play, Trash2, Edit, Eye } from 'lucide-react'
import { toast } from 'react-hot-toast'
import { Tool } from '@/types'
import { api } from '@/services/api'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Badge } from '@/components/ui/Badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/DropdownMenu'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/Dialog'
import LoadingSpinner from '@/components/ui/LoadingSpinner'

const ToolsPage: React.FC = () => {
  const navigate = useNavigate()
  const [tools, setTools] = useState<Tool[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'INTERNAL' | 'EXTERNAL' | 'API' | 'WEBHOOK' | 'ALL'>('ALL')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [toolToDelete, setToolToDelete] = useState<Tool | null>(null)
  const [deleting, setDeleting] = useState(false)

  useEffect(() => {
    loadTools()
  }, [])

  const loadTools = async () => {
    try {
      setLoading(true)
      const data = await api.getTools()
      setTools(data)
    } catch (error) {
      console.error('Failed to load tools:', error)
      toast.error('Failed to load tools')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteTool = async () => {
    if (!toolToDelete) return

    try {
      setDeleting(true)
      await api.deleteTool(toolToDelete.id)
      setTools(tools.filter(tool => tool.id !== toolToDelete.id))
      toast.success('Tool deleted successfully')
      setDeleteDialogOpen(false)
      setToolToDelete(null)
    } catch (error) {
      console.error('Failed to delete tool:', error)
      toast.error('Failed to delete tool')
    } finally {
      setDeleting(false)
    }
  }

  const handleExecuteTool = async (tool: Tool) => {
    try {
      // For demo purposes, execute with empty input
      const result = await api.executeTool(tool.id, {})
      toast.success('Tool executed successfully')
      console.log('Tool execution result:', result)
    } catch (error) {
      console.error('Failed to execute tool:', error)
      toast.error('Failed to execute tool')
    }
  }

  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tool.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'ALL' || tool.type === filterType
    return matchesSearch && matchesType
  })

  const getTypeColor = (type: 'INTERNAL' | 'EXTERNAL' | 'API' | 'WEBHOOK') => {
    switch (type) {
      case 'INTERNAL': return 'bg-blue-100 text-blue-800'
      case 'EXTERNAL': return 'bg-green-100 text-green-800'
      case 'API': return 'bg-purple-100 text-purple-800'
      case 'WEBHOOK': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Tools</h1>
          <p className="text-gray-600 mt-1">Manage and configure your AI tools</p>
        </div>
        <Button onClick={() => navigate('/tools/new')} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Tool
        </Button>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search tools..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value as 'INTERNAL' | 'EXTERNAL' | 'API' | 'WEBHOOK' | 'ALL')}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="ALL">All Types</option>
          <option value="INTERNAL">Internal</option>
          <option value="EXTERNAL">External</option>
          <option value="API">API</option>
          <option value="WEBHOOK">Webhook</option>
        </select>
      </div>

      {/* Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTools.map((tool) => (
          <Card key={tool.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-2">
              <div className="flex-1">
                <CardTitle className="text-lg font-semibold">{tool.name}</CardTitle>
                <Badge className={`mt-2 ${getTypeColor(tool.type as 'INTERNAL' | 'EXTERNAL' | 'API' | 'WEBHOOK')}`}>
                  {tool.type}
                </Badge>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => navigate(`/tools/${tool.id}`)}>
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate(`/tools/${tool.id}/edit`)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExecuteTool(tool)}>
                    <Play className="h-4 w-4 mr-2" />
                    Execute
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => {
                      setToolToDelete(tool)
                      setDeleteDialogOpen(true)
                    }}
                    className="text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                {tool.description || 'No description available'}
              </p>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span className={`px-2 py-1 rounded-full text-xs ${tool.status === 'ACTIVE'
                  ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                  {tool.status === 'ACTIVE' ? 'Active' : 'Inactive'}
                </span>
                <span>Updated {new Date(tool.updatedAt).toLocaleDateString()}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTools.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">No tools found</div>
          <p className="text-gray-600 mb-4">
            {searchTerm || filterType !== 'ALL'
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first tool'
            }
          </p>
          {!searchTerm && filterType === 'ALL' && (
            <Button onClick={() => navigate('/tools/new')}>
              Create Your First Tool
            </Button>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Tool</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{toolToDelete?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteTool}
              disabled={deleting}
            >
              {deleting ? 'Deleting...' : 'Delete'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ToolsPage

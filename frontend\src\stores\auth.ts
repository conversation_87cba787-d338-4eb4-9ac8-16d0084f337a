import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, AuthResponse, LoginForm, RegisterForm } from '@/types'
import { api } from '@/services/api'
import { websocket } from '@/services/websocket'
import { toast } from 'react-hot-toast'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // Actions
  login: (credentials: LoginForm) => Promise<void>
  register: (userData: RegisterForm) => Promise<void>
  logout: () => void
  refreshUser: () => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials: LoginForm) => {
        try {
          set({ isLoading: true, error: null })

          const response: AuthResponse = await api.login(credentials)

          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })

          // Connect to WebSocket
          websocket.reconnectWithToken(response.token)
          
          toast.success(`Welcome back, ${response.user.name || response.user.email}!`)
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || 'Login failed'
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          })
          throw error
        }
      },

      register: async (userData: RegisterForm) => {
        try {
          set({ isLoading: true, error: null })
          
          const response: AuthResponse = await api.register(userData)
          
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })

          // Connect to WebSocket
          websocket.reconnectWithToken(response.token)
          
          toast.success(`Welcome to SynapseAI, ${response.user.name || response.user.email}!`)
        } catch (error: any) {
          const errorMessage = error.response?.data?.error || error.message || 'Registration failed'
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage
          })
          throw error
        }
      },

      logout: () => {
        api.logout()
        websocket.disconnect()
        
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })
        
        toast.success('Logged out successfully')
      },

      refreshUser: async () => {
        try {
          set({ isLoading: true, error: null })
          
          const user = await api.getCurrentUser()
          
          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
        } catch (error: any) {
          // If refresh fails, user is likely not authenticated
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          })
          
          // Don't throw error for refresh failures
          console.warn('Failed to refresh user:', error)
        }
      },

      clearError: () => {
        set({ error: null })
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      }),
      onRehydrateStorage: () => (state) => {
        // Auto-refresh user data on app load if authenticated
        if (state?.isAuthenticated && state?.user) {
          state.refreshUser()
        }
      }
    }
  )
)

// Selectors for easier access
export const useUser = () => useAuthStore((state) => state.user)
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated)
export const useAuthLoading = () => useAuthStore((state) => state.isLoading)
export const useAuthError = () => useAuthStore((state) => state.error)
